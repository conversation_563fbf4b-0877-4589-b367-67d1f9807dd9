#!/bin/bash

# Windows交叉编译环境配置脚本
# 用于在Linux系统上配置Windows交叉编译环境

set -e

echo "=== Linux等保基线核查工具 - Windows交叉编译环境配置 ==="
echo ""

# 检查系统
echo "1. 检查系统信息..."
uname -a
echo ""

# 检查Go环境
echo "2. 检查Go环境..."
go version
echo "GOOS: $(go env GOOS)"
echo "GOARCH: $(go env GOARCH)"
echo "CGO_ENABLED: $(go env CGO_ENABLED)"
echo ""

# 检查现有编译器
echo "3. 检查现有编译器..."
if command -v gcc &> /dev/null; then
    echo "GCC版本: $(gcc --version | head -n1)"
else
    echo "GCC: 未安装"
fi

if command -v x86_64-w64-mingw32-gcc &> /dev/null; then
    echo "MinGW-w64版本: $(x86_64-w64-mingw32-gcc --version | head -n1)"
    MINGW_AVAILABLE=true
else
    echo "MinGW-w64: 未安装"
    MINGW_AVAILABLE=false
fi
echo ""

# 检查Fyne工具
echo "4. 检查Fyne工具..."
if command -v ~/go/bin/fyne &> /dev/null; then
    echo "Fyne CLI版本: $(~/go/bin/fyne version)"
else
    echo "Fyne CLI: 未安装"
fi
echo ""

# 尝试安装MinGW-w64
echo "5. 尝试安装MinGW-w64..."
if [ "$MINGW_AVAILABLE" = false ]; then
    echo "正在尝试安装MinGW-w64..."
    
    # 检测包管理器
    if command -v apt-get &> /dev/null; then
        echo "使用apt-get安装..."
        sudo apt-get update && sudo apt-get install -y gcc-mingw-w64
    elif command -v yum &> /dev/null; then
        echo "使用yum安装..."
        sudo yum install -y mingw64-gcc mingw64-gcc-c++
    elif command -v dnf &> /dev/null; then
        echo "使用dnf安装..."
        sudo dnf install -y mingw64-gcc mingw64-gcc-c++
    elif command -v pacman &> /dev/null; then
        echo "使用pacman安装..."
        sudo pacman -S mingw-w64-gcc
    else
        echo "未找到支持的包管理器，请手动安装MinGW-w64"
        echo ""
        echo "手动安装指南："
        echo "1. 下载MinGW-w64预编译版本"
        echo "2. 解压到 /opt/mingw64"
        echo "3. 添加 /opt/mingw64/bin 到PATH"
        exit 1
    fi
    
    # 验证安装
    if command -v x86_64-w64-mingw32-gcc &> /dev/null; then
        echo "MinGW-w64安装成功！"
        MINGW_AVAILABLE=true
    else
        echo "MinGW-w64安装失败"
        MINGW_AVAILABLE=false
    fi
fi

echo ""

# 测试Windows编译
echo "6. 测试Windows交叉编译..."
if [ "$MINGW_AVAILABLE" = true ]; then
    echo "尝试编译Windows版本..."
    
    # 设置交叉编译环境变量
    export CGO_ENABLED=1
    export GOOS=windows
    export GOARCH=amd64
    export CC=x86_64-w64-mingw32-gcc
    export CXX=x86_64-w64-mingw32-g++
    
    # 创建构建目录
    mkdir -p build
    
    # 尝试编译
    if go build -ldflags="-H windowsgui" -o build/linux-security-checker.exe ./cmd; then
        echo "✅ Windows交叉编译成功！"
        echo "输出文件: build/linux-security-checker.exe"
        ls -la build/linux-security-checker.exe
    else
        echo "❌ Windows交叉编译失败"
        echo ""
        echo "可能的解决方案："
        echo "1. 确保所有依赖都支持Windows交叉编译"
        echo "2. 检查CGO相关的库是否有Windows版本"
        echo "3. 考虑在Windows环境下直接编译"
    fi
else
    echo "❌ 无法进行Windows交叉编译，MinGW-w64未安装"
fi

echo ""
echo "=== 配置完成 ==="
echo ""
echo "如果交叉编译失败，建议："
echo "1. 在Windows环境下直接编译"
echo "2. 使用Docker容器进行编译"
echo "3. 使用GitHub Actions等CI/CD服务"
echo ""
echo "Windows环境编译命令："
echo "  go build -o linux-security-checker.exe ./cmd"
echo ""
