package main

import (
	"image"
	"image/color"
	"image/png"
	"os"
)

func main() {
	// 创建一个64x64的图像
	img := image.NewRGBA(image.Rect(0, 0, 64, 64))
	
	// 填充蓝色背景
	blue := color.RGBA{0, 0, 255, 255}
	for y := 0; y < 64; y++ {
		for x := 0; x < 64; x++ {
			img.Set(x, y, blue)
		}
	}
	
	// 创建文件
	file, err := os.Create("Icon.png")
	if err != nil {
		panic(err)
	}
	defer file.Close()
	
	// 编码为PNG
	err = png.Encode(file, img)
	if err != nil {
		panic(err)
	}
	
	println("图标文件 Icon.png 已创建")
}
