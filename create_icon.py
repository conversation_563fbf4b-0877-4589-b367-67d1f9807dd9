#!/usr/bin/env python3
from PIL import Image, ImageDraw, ImageFont
import os

# 创建一个256x256的蓝色背景图像
img = Image.new('RGB', (256, 256), color='blue')
draw = ImageDraw.Draw(img)

# 在图像中心绘制白色文字"GUI"
try:
    # 尝试使用系统字体
    font = ImageFont.truetype("/usr/share/fonts/dejavu/DejaVuSans-Bold.ttf", 72)
except:
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 72)
    except:
        # 使用默认字体
        font = ImageFont.load_default()

# 获取文字尺寸
text = "GUI"
bbox = draw.textbbox((0, 0), text, font=font)
text_width = bbox[2] - bbox[0]
text_height = bbox[3] - bbox[1]

# 计算居中位置
x = (256 - text_width) // 2
y = (256 - text_height) // 2

# 绘制文字
draw.text((x, y), text, fill='white', font=font)

# 保存图像
img.save('Icon.png')
print("图标文件 Icon.png 已创建")
