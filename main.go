package main

import (
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func main() {
	myApp := app.New()
	myWindow := myApp.NewWindow("简单的Fyne GUI程序")
	myWindow.Resize(fyne.NewSize(400, 300))

	title := widget.NewLabel("欢迎使用Fyne GUI!")
	title.Alignment = fyne.TextAlignCenter

	content := widget.NewLabel("这是一个用Go和Fyne创建的简单GUI程序")
	content.Wrapping = fyne.TextWrapWord

	button := widget.NewButton("点击我", func() {
		content.SetText("按钮被点击了！Fyne GUI程序运行正常。")
	})

	resetButton := widget.NewButton("重置", func() {
		content.SetText("这是一个用Go和Fyne创建的简单GUI程序")
	})

	buttonContainer := container.NewHBox(button, resetButton)

	mainContainer := container.NewVBox(
		title,
		widget.NewSeparator(),
		content,
		widget.NewSeparator(),
		buttonContainer,
	)

	myWindow.SetContent(mainContainer)
	myWindow.ShowAndRun()
}
