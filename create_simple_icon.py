#!/usr/bin/env python3
import struct

def create_simple_png():
    # 创建一个简单的32x32蓝色PNG图像
    width, height = 32, 32
    
    # PNG文件头
    png_signature = b'\x89PNG\r\n\x1a\n'
    
    # IHDR chunk
    ihdr_data = struct.pack('>IIBBBBB', width, height, 8, 2, 0, 0, 0)
    ihdr_crc = 0x2144df1c  # 预计算的CRC
    ihdr_chunk = struct.pack('>I', 13) + b'IHDR' + ihdr_data + struct.pack('>I', ihdr_crc)
    
    # 简单的蓝色像素数据 (RGB)
    pixel_data = b''
    for y in range(height):
        pixel_data += b'\x00'  # 过滤器类型
        for x in range(width):
            pixel_data += b'\x00\x00\xff'  # 蓝色像素 (RGB)
    
    # 压缩像素数据 (简化版)
    import zlib
    compressed_data = zlib.compress(pixel_data)
    
    # IDAT chunk
    idat_chunk = struct.pack('>I', len(compressed_data)) + b'IDAT' + compressed_data
    idat_crc = zlib.crc32(b'IDAT' + compressed_data) & 0xffffffff
    idat_chunk += struct.pack('>I', idat_crc)
    
    # IEND chunk
    iend_chunk = struct.pack('>I', 0) + b'IEND' + struct.pack('>I', 0xae426082)
    
    # 组合完整的PNG文件
    png_data = png_signature + ihdr_chunk + idat_chunk + iend_chunk
    
    with open('Icon.png', 'wb') as f:
        f.write(png_data)
    
    print("简单图标文件 Icon.png 已创建")

if __name__ == "__main__":
    create_simple_png()
